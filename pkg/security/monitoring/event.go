package monitoring

type SecurityEventType string

const (
	SecurityEventTypeAuthentication SecurityEventType = "authentication"
	SecurityEventTypeAuthorization SecurityEventType = "authorization"
	SecurityEventTypeAccess       SecurityEventType = "access"
	SecurityEventTypeAudit        SecurityEventType = "audit"
)

type SecuritySeverity string

const (
	SeverityLow    SecuritySeverity = "low"
	SeverityMedium SecuritySeverity = "medium"
	SeverityHigh   SecuritySeverity = "high"
	SeverityInfo   SecuritySeverity = "info"
)

type SecurityEvent struct {
	EventType   SecurityEventType
	Severity    SecuritySeverity
	Source      string
	UserID      string
	Description string
	Metadata    map[string]interface{}
}

type SecurityMonitoringService struct {
	// ... implementation details
}

func (s *SecurityMonitoringService) LogSecurityEvent(ctx context.Context, event *SecurityEvent) {
	// ... implementation details
}
